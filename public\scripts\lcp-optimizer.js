/**
 * LCP (Largest Contentful Paint) Optimizer
 * Ultra-high priority script to optimize LCP performance
 * Must be loaded synchronously and executed immediately
 */

(function() {
  'use strict';
  
  // LCP optimization configuration
  const LCP_CONFIG = {
    targetElements: ['#main-product-image', '#lcp-image', '.lcp-image'],
    maxWaitTime: 100, // Maximum time to wait for element (ms)
    forceOptimization: true,
    enableMonitoring: true
  };
  
  /**
   * Force immediate visibility of LCP elements
   */
  function optimizeLCPElements() {
    LCP_CONFIG.targetElements.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element) {
          // Force immediate visibility
          element.style.opacity = '1';
          element.style.visibility = 'visible';
          element.style.display = 'block';
          
          // Remove render-blocking effects
          element.style.transition = 'none';
          element.style.animation = 'none';
          element.style.transform = 'none';
          element.style.filter = 'none';
          
          // Optimize rendering
          element.style.imageRendering = '-webkit-optimize-contrast';
          element.style.willChange = 'auto';
          element.style.backfaceVisibility = 'hidden';
          
          // Mark as optimized
          element.setAttribute('data-lcp-optimized', 'true');
          element.setAttribute('data-lcp-time', Date.now());
        }
      });
    });
  }
  
  /**
   * Preload critical LCP images
   */
  function preloadLCPImages() {
    // Find image sources from meta tags or data attributes
    const lcpImageMeta = document.querySelector('meta[name="lcp-image"]');
    const lcpImageUrl = lcpImageMeta ? lcpImageMeta.content : null;
    
    if (lcpImageUrl) {
      const img = new Image();
      img.src = lcpImageUrl;
      img.fetchPriority = 'high';
      img.loading = 'eager';
      img.decoding = 'sync';
      
      // Force decode if supported
      if (img.decode) {
        img.decode().catch(() => {
          console.warn('LCP image decode failed');
        });
      }
    }
    
    // Also preload any visible images with high priority
    const visibleImages = document.querySelectorAll('img[fetchpriority="high"], img[loading="eager"]');
    visibleImages.forEach(img => {
      if (img.src && !img.complete) {
        const preloadImg = new Image();
        preloadImg.src = img.src;
        preloadImg.fetchPriority = 'high';
        preloadImg.loading = 'eager';
        preloadImg.decoding = 'sync';
      }
    });
  }
  
  /**
   * Remove render-blocking CSS for LCP elements
   */
  function removeRenderBlocking() {
    // Add critical CSS for LCP if not already present
    const existingStyle = document.querySelector('#lcp-critical-css');
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = 'lcp-critical-css';
      style.textContent = `
        #main-product-image,#lcp-image,.lcp-image {
          opacity: 1 !important;
          visibility: visible !important;
          display: block !important;
          transition: none !important;
          animation: none !important;
          transform: none !important;
          filter: none !important;
          width: 100% !important;
          height: 100% !important;
          object-fit: cover !important;
          image-rendering: -webkit-optimize-contrast;
          will-change: auto;
          backface-visibility: hidden;
        }
        .lcp-container {
          position: relative;
          width: 100%;
          background: #f8fafc;
          border-radius: 0.5rem;
          overflow: hidden;
          contain: layout style paint;
        }
      `;
      document.head.insertBefore(style, document.head.firstChild);
    }
  }
  
  /**
   * Monitor LCP performance
   */
  function monitorLCP() {
    if (!LCP_CONFIG.enableMonitoring || !('PerformanceObserver' in window)) {
      return;
    }
    
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        if (lastEntry) {
          console.log(`LCP: ${lastEntry.startTime.toFixed(2)}ms`);
          
          // Mark LCP element if available
          if (lastEntry.element) {
            lastEntry.element.setAttribute('data-lcp-measured', lastEntry.startTime.toFixed(2));
            
            // Log if LCP is our optimized element
            if (lastEntry.element.hasAttribute('data-lcp-optimized')) {
              console.log('✅ LCP element was optimized');
            } else {
              console.warn('⚠️ LCP element was not optimized');
            }
          }
        }
      });
      
      observer.observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (error) {
      console.warn('LCP monitoring failed:', error);
    }
  }
  
  /**
   * Initialize LCP optimization
   */
  function initLCPOptimization() {
    // Run optimizations immediately
    removeRenderBlocking();
    preloadLCPImages();
    optimizeLCPElements();
    
    // Set up monitoring
    if (LCP_CONFIG.enableMonitoring) {
      monitorLCP();
    }
    
    // Retry optimization after a short delay
    setTimeout(() => {
      optimizeLCPElements();
    }, LCP_CONFIG.maxWaitTime);
    
    // Final optimization on DOM ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', optimizeLCPElements);
    }
    
    console.log('🚀 LCP optimization initialized');
  }
  
  /**
   * Expose LCP optimizer for manual use
   */
  window.LCPOptimizer = {
    optimize: optimizeLCPElements,
    preload: preloadLCPImages,
    monitor: monitorLCP,
    config: LCP_CONFIG
  };
  
  // Initialize immediately
  initLCPOptimization();
  
})();
