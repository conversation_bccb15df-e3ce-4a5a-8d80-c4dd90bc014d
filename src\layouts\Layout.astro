---
import GoogleAnalytics from '../components/GoogleAnalytics.astro';
import ToastNotification from '../components/ToastNotification.astro';
import OrganizationSchema from '../components/seo/OrganizationSchema.astro';
import OptimizedSnipcartLoader from '../components/OptimizedSnipcartLoader.astro';
import OptimizedCSSLoader from '../components/OptimizedCSSLoader.astro';
import LCPOptimizer from '../components/LCPOptimizer.astro';
import CriticalRequestOptimizer from '../components/CriticalRequestOptimizer.astro';
import PerformanceMonitor from '../components/PerformanceMonitor.astro';
// Import all CSS files directly for proper processing and MIME types
import '../assets/css/variables.css';
import '../assets/css/base.css';
import '../assets/css/components.css';
import '../assets/css/header.css';
import '../assets/css/products.css';
import '../assets/css/responsive.css';
import '../assets/css/modules/buttons.css';
import '../assets/css/modules/forms.css';
import '../assets/css/modules/layout.css';

export interface Props {
	title: string;
	description?: string;
	image?: string;
	noIndex?: boolean;
}

const { title, description = "Cheers Marketplace - Quality secondhand goods in Chico, CA", image = "/cheers-marketplace-og.jpg", noIndex = false } = Astro.props;

// Generate canonical URL with trailing slash to match trailingSlash: 'always' config
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
// Ensure trailing slash for consistency with Astro config
if (!canonicalURL.pathname.endsWith('/') && !canonicalURL.pathname.includes('.')) {
  canonicalURL.pathname += '/';
}
---

<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<meta name="generator" content={Astro.generator} />

	<!-- Performance optimizations for mobile -->
	<meta name="format-detection" content="telephone=no" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="default" />

	<!-- Optimized font loading strategy - using system fonts for maximum performance -->
	<!-- System fonts provide instant loading with no external requests -->

	<!-- Critical Request Chain Optimization -->
	<CriticalRequestOptimizer
		enableResourceHints={true}
		enablePreloading={true}
		enablePrefetching={true}
	/>

	<!-- LCP Optimization -->
	<LCPOptimizer
		heroImage="/logo.png"
		enablePreloading={true}
		enablePriorityHints={true}
		enableResourceHints={true}
	/>

	<!-- System fonts only for maximum performance -->
	<!-- Google Fonts removed to eliminate third-party blocking -->

	<!-- Critical CSS inlined for instant rendering -->
	<style>
		/* Critical above-the-fold styles - optimized for performance */
		:root{--primary:#92400e;--primary-dark:#78350f;--primary-light:#d97706;--secondary:#1e293b;--background:#f8fafc;--light-background:#ffffff;--card-bg:#ffffff;--text:#0f172a;--text-secondary:#475569;--border:#e2e8f0;--radius:0.5rem;--radius-lg:0.75rem;--radius-xl:1rem;--container-width:1200px;--shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--font-system:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-serif:Georgia,"Times New Roman",serif}
		*,*::before,*::after{box-sizing:border-box}
		html,body{margin:0;padding:0;font-family:var(--font-system);background:var(--background);color:var(--text);line-height:1.6;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
		.container{max-width:var(--container-width);margin:0 auto;padding:0 2rem}
		h1,h2,h3{font-family:var(--font-serif);color:var(--text);font-weight:600;line-height:1.2;margin:0 0 1rem 0}
		h1{font-size:2.5rem}h2{font-size:2rem}h3{font-size:1.5rem}
		.site-header{background:var(--light-background);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:100;backdrop-filter:blur(10px)}
		.header-flex{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;min-height:70px;gap:1rem}
		.logo{display:flex;align-items:center;gap:0.75rem;text-decoration:none;color:var(--text);font-weight:600;font-size:1.25rem;flex-shrink:0;margin-right:1rem}
		.main-nav{display:flex;gap:3.5rem;align-items:center;flex:1;justify-content:center;margin:0 2rem}
		.main-nav a{text-decoration:none;color:var(--text-secondary);font-weight:500;transition:color 0.2s ease;padding:0.75rem 1rem;border-radius:var(--radius);white-space:nowrap;font-size:0.95rem}
		.main-nav a:hover{color:var(--primary)}
		.btn{display:inline-flex;align-items:center;justify-content:center;padding:0.75rem 1.5rem;border:none;border-radius:var(--radius);font-weight:500;text-decoration:none;transition:all 0.2s ease;cursor:pointer;font-size:0.875rem}
		.btn.primary{background:var(--primary);color:white}
		.btn.primary:hover{background:var(--primary-dark)}
		.cart-trigger{position:relative;background:none;border:none;padding:0.5rem;cursor:pointer;color:var(--text-secondary);transition:color 0.2s ease}
		.cart-trigger:hover{color:var(--primary)}
		.cart-count{position:absolute;top:-5px;right:-5px;background:var(--primary);color:white;border-radius:50%;width:20px;height:20px;font-size:0.75rem;display:flex;align-items:center;justify-content:center;font-weight:600}
		.mobile-menu-toggle{display:none;flex-direction:column;gap:4px;background:none;border:none;padding:0.5rem;cursor:pointer}
		.hamburger-line{width:24px;height:2px;background:var(--text);transition:all 0.3s ease}
		/* Critical homepage styles for LCP optimization */
		.hero-section{padding:2rem 0;text-align:center;max-width:800px;margin:0 auto}
		.hero-content h1{font-size:2.5rem;margin-bottom:0.5rem;color:var(--text)}
		.hero-subtitle{font-size:1.25rem;color:var(--primary);font-weight:600;margin-bottom:1rem}
		.hero-description{font-size:1.125rem;color:var(--text-secondary);line-height:1.6;max-width:600px;margin:0 auto}
		.about-preview{background:var(--card-bg);border-radius:var(--radius-xl);box-shadow:var(--shadow-lg);padding:2rem;margin:2rem auto;max-width:800px;border:1px solid var(--border)}
		.about-content{display:grid;grid-template-columns:1fr auto;gap:2rem;align-items:center}
		.about-text h2{font-size:1.75rem;margin-bottom:1rem;color:var(--text)}
		.about-text p{color:var(--text-secondary);line-height:1.6;margin:0}
		.family-badge{display:flex;flex-direction:column;align-items:center;gap:0.5rem;background:var(--primary);color:white;padding:1rem;border-radius:var(--radius-lg);text-align:center;min-width:100px}
		.family-badge span{font-size:0.75rem;font-weight:600;text-transform:uppercase;letter-spacing:0.05em}
		/* Additional critical styles for LCP optimization */
		.features-section{padding:2rem 0;max-width:1000px;margin:0 auto}
		.features-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:1.25rem}
		.feature-card{background:var(--card-bg);border-radius:var(--radius-lg);padding:1.5rem;text-align:center;border:1px solid var(--border);transition:transform 0.2s ease}
		.feature-icon{color:var(--primary);margin-bottom:1rem;display:flex;justify-content:center}
		.feature-card h3{font-size:1.125rem;margin-bottom:0.75rem;color:var(--text)}
		.feature-card p{color:var(--text-secondary);font-size:0.9rem;line-height:1.5;margin:0}
		@media (max-width: 768px){.container{padding:0 1rem}.header-flex{padding:1rem}h1{font-size:2rem}h2{font-size:1.75rem}h3{font-size:1.25rem}.hero-content h1{font-size:2rem}.hero-subtitle{font-size:1.125rem}.about-content{grid-template-columns:1fr;text-align:center}.about-preview{padding:1.5rem;margin:1.5rem auto}.features-grid{grid-template-columns:1fr;gap:1rem}.feature-card{padding:1.25rem}}
		.loading{opacity:0.6;pointer-events:none}
		.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}
		*:focus-visible{outline:2px solid var(--primary);outline-offset:2px}
		/* CRITICAL: LCP image optimization only - preserve layout */
		#main-product-image{opacity:1!important;visibility:visible!important;transition:none!important;animation:none!important}
	</style>

	<!-- Optimized CSS Loading (disabled to prevent 404 errors) -->
	<OptimizedCSSLoader
		nonCriticalCSS={[]}
		enablePreload={false}
		enablePrefetch={false}
	/>

	<!-- Dynamic meta tags -->
	<title>{title}</title>
	<meta name="description" content={description} />
	<meta name="keywords" content="cheap used goods, affordable secondhand, quality thrift, budget items, Chico CA, family business, pre-owned, discount goods, bargain finds, low cost, inexpensive, economical, value shopping" />
	<meta name="author" content="Cheers Marketplace" />
	<meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
	<link rel="canonical" href={canonicalURL} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={canonicalURL} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={new URL(image, Astro.site)} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={canonicalURL} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={new URL(image, Astro.site)} />

	<!-- Google Analytics -->
	<GoogleAnalytics measurementId="G-5FH6Y2MPJN" />

	<!-- Organization Structured Data -->
	<OrganizationSchema />



	<!-- Page-specific head content -->
	<slot name="head" />
</head>
	<body>
		<slot />

		<!-- Toast Notifications -->
		<ToastNotification />

		<!-- Optimized Snipcart Integration -->
		<OptimizedSnipcartLoader />

		<!-- Performance Monitoring -->
		<PerformanceMonitor
			enableCoreWebVitals={true}
			enableResourceTiming={true}
			enableUserTiming={true}
			enableErrorTracking={true}
			sampleRate={0.1}
		/>
	</body>
</html>


